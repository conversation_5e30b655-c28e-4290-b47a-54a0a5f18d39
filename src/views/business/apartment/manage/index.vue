<script setup lang="tsx">
import { NDivider, NTag } from 'naive-ui';
import { roomStatusRecord, roomStatusTagColors } from '@/constants/apartment';
import { fetchBatchDeleteApartment, fetchGetApartmentList } from '@/service/api/business/apartment';
import { useAppStore } from '@/store/modules/app';
import { useAuth } from '@/hooks/business/auth';
import { useDownload } from '@/hooks/business/download';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import ButtonIcon from '@/components/custom/button-icon.vue';
import ApartmentOperateDrawer from './modules/apartment-operate-drawer.vue';
import ApartmentSearch from './modules/apartment-search.vue';

defineOptions({
  name: 'ApartmentList'
});

const appStore = useAppStore();
const { download } = useDownload();
const { hasAuth } = useAuth();

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchGetApartmentList,
  apiParams: {
    pageNum: 1,
    pageSize: 10,
    // if you want to use the searchParams in Form, you need to define the following properties, and the value is null
    // the value can not be undefined, otherwise the property in Form will not be reactive
    buildingNumber: null,
    floorNumber: null,
    roomNumber: null,
    houseType: null,
    area: null,
    facilities: null,
    roomStatus: null,
    occupancyLimit: null,
    mainImageUrl: null,
    imageUrls: null,
    params: {}
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'buildingNumber',
      title: '楼栋号',
      align: 'center',
      width: 100
    },
    {
      key: 'floorNumber',
      title: '楼层号',
      align: 'center',
      width: 100
    },
    {
      key: 'roomNumber',
      title: '房间号',
      align: 'center',
      width: 100
    },
    {
      key: 'houseType',
      title: '户型',
      align: 'center',
      width: 100
    },
    {
      key: 'area',
      title: '面积(㎡)',
      align: 'center',
      width: 100,
      render: row => (row.area ? `${row.area}㎡` : '-')
    },
    {
      key: 'roomStatus',
      title: '房间状态',
      align: 'center',
      width: 100,
      render: row => {
        const status = row.roomStatus;
        const text = roomStatusRecord[status] || status;
        const type = (roomStatusTagColors[status] || 'default') as
          | 'default'
          | 'success'
          | 'error'
          | 'warning'
          | 'primary'
          | 'info';
        return <NTag type={type}>{text}</NTag>;
      }
    },
    {
      key: 'occupancyLimit',
      title: '入住人数',
      align: 'center',
      width: 100,
      render: row => (row.occupancyLimit ? `${row.occupancyLimit}人` : '-')
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 130,
      render: row => {
        const divider = () => {
          if (!hasAuth('business:apartment:edit') || !hasAuth('business:apartment:remove')) {
            return null;
          }
          return <NDivider vertical />;
        };

        const editBtn = () => {
          if (!hasAuth('business:apartment:edit')) {
            return null;
          }
          return (
            <ButtonIcon
              text
              type="primary"
              icon="material-symbols:drive-file-rename-outline-outline"
              tooltipContent={$t('common.edit')}
              onClick={() => edit(row.id!)}
            />
          );
        };

        const deleteBtn = () => {
          if (!hasAuth('business:apartment:remove')) {
            return null;
          }
          return (
            <ButtonIcon
              text
              type="error"
              icon="material-symbols:delete-outline"
              tooltipContent={$t('common.delete')}
              popconfirmContent={$t('common.confirmDelete')}
              onPositiveClick={() => handleDelete(row.id!)}
            />
          );
        };

        return (
          <div class="flex-center gap-8px">
            {editBtn()}
            {divider()}
            {deleteBtn()}
          </div>
        );
      }
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  // request
  const { error } = await fetchBatchDeleteApartment(checkedRowKeys.value);
  if (error) return;
  onBatchDeleted();
}

async function handleDelete(id: CommonType.IdType) {
  // request
  const { error } = await fetchBatchDeleteApartment([id]);
  if (error) return;
  onDeleted();
}

function edit(id: CommonType.IdType) {
  handleEdit('id', id);
}

function handleExport() {
  download('/business/apartmentInfo/export', searchParams, `公寓信息管理_${new Date().getTime()}.xlsx`);
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <ApartmentSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard title="公寓信息管理" :bordered="false" size="small" class="card-wrapper sm:flex-1-hidden">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          :show-add="hasAuth('business:apartment:add')"
          :show-delete="hasAuth('business:apartment:remove')"
          :show-export="hasAuth('business:apartment:export')"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @export="handleExport"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="800"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <ApartmentOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
