<script setup lang="ts">
import { ref } from 'vue';
import { useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'TenantSearch'
});

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
}

const emit = defineEmits<Emits>();

const { formRef, validate, restoreValidation } = useNaiveForm();


const model = defineModel<Api.Business.TenantSearchParams>('model', { required: true });


async function reset() {
  Object.assign(model.value.params!, {});
  await restoreValidation();
  emit('reset');
}

async function search() {
  await validate();
  emit('search');
}
</script>

<template>
  <NCard :bordered="false" size="small" class="card-wrapper">
    <NCollapse>
      <NCollapseItem :title="$t('common.search')" name="user-search">
        <NForm ref="formRef" :model="model" label-placement="left" :label-width="80">
          <NGrid responsive="screen" item-responsive>
            <NFormItemGi span="24 s:12 m:6" label="租户编号，系统自动生成，如TEN20231015001" path="tenantNo" class="pr-24px">
              <NInput v-model:value="model.tenantNo" placeholder="请输入租户编号，系统自动生成，如TEN20231015001" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="租户姓名" path="name" class="pr-24px">
              <NInput v-model:value="model.name" placeholder="请输入租户姓名" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="性别" path="gender" class="pr-24px">
              <NInput v-model:value="model.gender" placeholder="请输入性别" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="身份证号码" path="idCardNumber" class="pr-24px">
              <NInput v-model:value="model.idCardNumber" placeholder="请输入身份证号码" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="身份证正面照片URL" path="idCardFrontUrl" class="pr-24px">
              <NInput v-model:value="model.idCardFrontUrl" placeholder="请输入身份证正面照片URL" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="身份证反面照片URL" path="idCardBackUrl" class="pr-24px">
              <NInput v-model:value="model.idCardBackUrl" placeholder="请输入身份证反面照片URL" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="出生日期" path="dateOfBirth" class="pr-24px">
              <NDatePicker
                v-model:formatted-value="model.dateOfBirth"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="民族" path="nation" class="pr-24px">
              <NInput v-model:value="model.nation" placeholder="请输入民族" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="联系方式" path="contactNumber" class="pr-24px">
              <NInput v-model:value="model.contactNumber" placeholder="请输入联系方式" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="紧急联系人姓名" path="emergencyContact" class="pr-24px">
              <NInput v-model:value="model.emergencyContact" placeholder="请输入紧急联系人姓名" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="紧急联系人电话" path="emergencyContactNumber" class="pr-24px">
              <NInput v-model:value="model.emergencyContactNumber" placeholder="请输入紧急联系人电话" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="公司信息" path="companyInfo" class="pr-24px">
              <NInput v-model:value="model.companyInfo" placeholder="请输入公司信息" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="户籍地址" path="householdRegister" class="pr-24px">
              <NInput v-model:value="model.householdRegister" placeholder="请输入户籍地址" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="现住址" path="currentAddress" class="pr-24px">
              <NInput v-model:value="model.currentAddress" placeholder="请输入现住址" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="入住时间" path="checkInTime" class="pr-24px">
              <NDatePicker
                v-model:formatted-value="model.checkInTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="退租时间" path="checkOutTime" class="pr-24px">
              <NDatePicker
                v-model:formatted-value="model.checkOutTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="租约类型：长期/短期" path="leaseTermType" class="pr-24px">
              <NSelect
                v-model:value="model.leaseTermType"
                placeholder="请选择租约类型：长期/短期"
                :options="[]"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="预计租期(月)，辅助判断租约类型" path="expectedLeaseTerm" class="pr-24px">
              <NInput v-model:value="model.expectedLeaseTerm" placeholder="请输入预计租期(月)，辅助判断租约类型" />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="租户状态" path="status" class="pr-24px">
              <NSelect
                v-model:value="model.status"
                placeholder="请选择租户状态"
                :options="[]"
                clearable
              />
            </NFormItemGi>
            <NFormItemGi span="24 s:12 m:6" label="头像存储路径" path="avatarUrl" class="pr-24px">
              <NInput v-model:value="model.avatarUrl" placeholder="请输入头像存储路径" />
            </NFormItemGi>
            <NFormItemGi span="24" class="pr-24px">
              <NSpace class="w-full" justify="end">
                <NButton @click="reset">
                  <template #icon>
                    <icon-ic-round-refresh class="text-icon" />
                  </template>
                  {{ $t('common.reset') }}
                </NButton>
                <NButton type="primary" ghost @click="search">
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  {{ $t('common.search') }}
                </NButton>
              </NSpace>
            </NFormItemGi>
          </NGrid>
        </NForm>
      </NCollapseItem>
    </NCollapse>
  </NCard>
</template>

<style scoped></style>
