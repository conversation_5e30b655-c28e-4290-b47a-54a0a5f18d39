<script setup lang="ts">
import { computed, reactive, watch } from 'vue';
import { fetchCreateTenant, fetchUpdateTenant } from '@/service/api/business/tenant';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({
  name: 'TenantOperateDrawer'
});

interface Props {
  /** the type of operation */
  operateType: NaiveUI.TableOperateType;
  /** the edit row data */
  rowData?: Api.Business.Tenant | null;
}

const props = defineProps<Props>();

interface Emits {
  (e: 'submitted'): void;
}

const emit = defineEmits<Emits>();

const visible = defineModel<boolean>('visible', {
  default: false
});


const { formRef, validate, restoreValidation } = useNaiveForm();
const { createRequiredRule } = useFormRules();

const title = computed(() => {
  const titles: Record<NaiveUI.TableOperateType, string> = {
    add: '新增租户信息管理',
    edit: '编辑租户信息管理'
  };
  return titles[props.operateType];
});

type Model = Api.Business.TenantOperateParams;

const model: Model = reactive(createDefaultModel());

function createDefaultModel(): Model {
  return {
      tenantNo: '',
      name: '',
      gender: '',
      idCardNumber: '',
      idCardFrontUrl: '',
      idCardBackUrl: '',
      dateOfBirth: undefined,
      nation: '',
      contactNumber: '',
      emergencyContact: '',
      emergencyContactNumber: '',
      companyInfo: '',
      householdRegister: '',
      currentAddress: '',
      checkInTime: undefined,
      checkOutTime: undefined,
      leaseTermType: '',
      expectedLeaseTerm: undefined,
      status: '',
      avatarUrl: '',
      remark: '',
  };
}

type RuleKey = Extract<
  keyof Model,
  | 'tenantNo'
  | 'name'
  | 'gender'
  | 'idCardNumber'
  | 'idCardFrontUrl'
  | 'idCardBackUrl'
  | 'dateOfBirth'
  | 'nation'
  | 'contactNumber'
  | 'emergencyContact'
  | 'emergencyContactNumber'
  | 'companyInfo'
  | 'householdRegister'
  | 'currentAddress'
  | 'checkInTime'
  | 'checkOutTime'
  | 'leaseTermType'
  | 'expectedLeaseTerm'
  | 'status'
  | 'avatarUrl'
  | 'remark'
  | 'createTime'
  | 'createBy'
  | 'updateTime'
  | 'updateBy'
  | 'version'
>;

const rules: Record<RuleKey, App.Global.FormRule> = {
  tenantNo: createRequiredRule('租户编号，系统自动生成，如TEN20231015001不能为空'),
  name: createRequiredRule('租户姓名不能为空'),
  gender: createRequiredRule('性别不能为空'),
  idCardNumber: createRequiredRule('身份证号码不能为空'),
  idCardFrontUrl: createRequiredRule('身份证正面照片URL不能为空'),
  idCardBackUrl: createRequiredRule('身份证反面照片URL不能为空'),
  dateOfBirth: createRequiredRule('出生日期不能为空'),
  nation: createRequiredRule('民族不能为空'),
  contactNumber: createRequiredRule('联系方式不能为空'),
  emergencyContact: createRequiredRule('紧急联系人姓名不能为空'),
  emergencyContactNumber: createRequiredRule('紧急联系人电话不能为空'),
  companyInfo: createRequiredRule('公司信息不能为空'),
  householdRegister: createRequiredRule('户籍地址不能为空'),
  currentAddress: createRequiredRule('现住址不能为空'),
  checkInTime: createRequiredRule('入住时间不能为空'),
  checkOutTime: createRequiredRule('退租时间不能为空'),
  leaseTermType: createRequiredRule('租约类型：长期/短期不能为空'),
  expectedLeaseTerm: createRequiredRule('预计租期(月)，辅助判断租约类型不能为空'),
  status: createRequiredRule('租户状态不能为空'),
  avatarUrl: createRequiredRule('头像存储路径不能为空'),
  remark: createRequiredRule('关于租户的备注信息不能为空'),
  createTime: createRequiredRule('记录创建时间不能为空'),
  createBy: createRequiredRule('创建该记录的人员不能为空'),
  updateTime: createRequiredRule('记录更新时间不能为空'),
  updateBy: createRequiredRule('更新该记录的人员不能为空'),
  version: createRequiredRule('版本号，用于乐观锁控制不能为空')
};

function handleUpdateModelWhenEdit() {
  if (props.operateType === 'add') {
    Object.assign(model, createDefaultModel());
    return;
  }

  if (props.operateType === 'edit' && props.rowData) {
    Object.assign(model, props.rowData);
  }
}

function closeDrawer() {
  visible.value = false;
}

async function handleSubmit() {
  await validate();

  const { id, tenantNo, name, gender, idCardNumber, idCardFrontUrl, idCardBackUrl, dateOfBirth, nation, contactNumber, emergencyContact, emergencyContactNumber, companyInfo, householdRegister, currentAddress, checkInTime, checkOutTime, leaseTermType, expectedLeaseTerm, status, avatarUrl, remark } = model;

  // request
  if (props.operateType === 'add') {
    const { error } = await fetchCreateTenant({ tenantNo, name, gender, idCardNumber, idCardFrontUrl, idCardBackUrl, dateOfBirth, nation, contactNumber, emergencyContact, emergencyContactNumber, companyInfo, householdRegister, currentAddress, checkInTime, checkOutTime, leaseTermType, expectedLeaseTerm, status, avatarUrl, remark });
    if (error) return;
  }

  if (props.operateType === 'edit') {
    const { error } = await fetchUpdateTenant({ id, tenantNo, name, gender, idCardNumber, idCardFrontUrl, idCardBackUrl, dateOfBirth, nation, contactNumber, emergencyContact, emergencyContactNumber, companyInfo, householdRegister, currentAddress, checkInTime, checkOutTime, leaseTermType, expectedLeaseTerm, status, avatarUrl, remark });
    if (error) return;
  }

  window.$message?.success($t('common.updateSuccess'));
  closeDrawer();
  emit('submitted');
}

watch(visible, () => {
  if (visible.value) {
    handleUpdateModelWhenEdit();
    restoreValidation();
  }
});
</script>

<template>
  <NDrawer v-model:show="visible" :title="title" display-directive="show" :width="800" class="max-w-90%">
    <NDrawerContent :title="title" :native-scrollbar="false" closable>
      <NForm ref="formRef" :model="model" :rules="rules">
        <NFormItem label="租户编号，系统自动生成，如TEN20231015001" path="tenantNo">
          <NInput v-model:value="model.tenantNo" placeholder="请输入租户编号，系统自动生成，如TEN20231015001" />
        </NFormItem>
        <NFormItem label="租户姓名" path="name">
          <NInput v-model:value="model.name" placeholder="请输入租户姓名" />
        </NFormItem>
        <NFormItem label="性别" path="gender">
          <NInput v-model:value="model.gender" placeholder="请输入性别" />
        </NFormItem>
        <NFormItem label="身份证号码" path="idCardNumber">
          <NInput v-model:value="model.idCardNumber" placeholder="请输入身份证号码" />
        </NFormItem>
        <NFormItem label="身份证正面照片URL" path="idCardFrontUrl">
          <NInput v-model:value="model.idCardFrontUrl" placeholder="请输入身份证正面照片URL" />
        </NFormItem>
        <NFormItem label="身份证反面照片URL" path="idCardBackUrl">
          <NInput v-model:value="model.idCardBackUrl" placeholder="请输入身份证反面照片URL" />
        </NFormItem>
        <NFormItem label="出生日期" path="dateOfBirth">
          <NDatePicker
            v-model:formatted-value="model.dateOfBirth"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            clearable
          />
        </NFormItem>
        <NFormItem label="民族" path="nation">
          <NInput v-model:value="model.nation" placeholder="请输入民族" />
        </NFormItem>
        <NFormItem label="联系方式" path="contactNumber">
          <NInput v-model:value="model.contactNumber" placeholder="请输入联系方式" />
        </NFormItem>
        <NFormItem label="紧急联系人姓名" path="emergencyContact">
          <NInput v-model:value="model.emergencyContact" placeholder="请输入紧急联系人姓名" />
        </NFormItem>
        <NFormItem label="紧急联系人电话" path="emergencyContactNumber">
          <NInput v-model:value="model.emergencyContactNumber" placeholder="请输入紧急联系人电话" />
        </NFormItem>
        <NFormItem label="公司信息" path="companyInfo">
          <NInput v-model:value="model.companyInfo" placeholder="请输入公司信息" />
        </NFormItem>
        <NFormItem label="户籍地址" path="householdRegister">
          <NInput v-model:value="model.householdRegister" placeholder="请输入户籍地址" />
        </NFormItem>
        <NFormItem label="现住址" path="currentAddress">
          <NInput v-model:value="model.currentAddress" placeholder="请输入现住址" />
        </NFormItem>
        <NFormItem label="入住时间" path="checkInTime">
          <NDatePicker
            v-model:formatted-value="model.checkInTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            clearable
          />
        </NFormItem>
        <NFormItem label="退租时间" path="checkOutTime">
          <NDatePicker
            v-model:formatted-value="model.checkOutTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            clearable
          />
        </NFormItem>
        <NFormItem label="租约类型：长期/短期" path="leaseTermType">
          <NInput v-model:value="model.leaseTermType" placeholder="请输入租约类型：长期/短期" />
        </NFormItem>
        <NFormItem label="预计租期(月)，辅助判断租约类型" path="expectedLeaseTerm">
          <NInput v-model:value="model.expectedLeaseTerm" placeholder="请输入预计租期(月)，辅助判断租约类型" />
        </NFormItem>
        <NFormItem label="租户状态" path="status">
          <NInput v-model:value="model.status" placeholder="请输入租户状态" />
        </NFormItem>
        <NFormItem label="头像存储路径" path="avatarUrl">
          <NInput v-model:value="model.avatarUrl" placeholder="请输入头像存储路径" />
        </NFormItem>
        <NFormItem label="关于租户的备注信息" path="remark">
          <NInput
            v-model:value="model.remark"
            :rows="3"
            type="textarea"
            placeholder="请输入关于租户的备注信息"
          />
        </NFormItem>
      </NForm>
      <template #footer>
        <NSpace :size="16">
          <NButton @click="closeDrawer">{{ $t('common.cancel') }}</NButton>
          <NButton type="primary" @click="handleSubmit">{{ $t('common.confirm') }}</NButton>
        </NSpace>
      </template>
    </NDrawerContent>
  </NDrawer>
</template>

<style scoped></style>
