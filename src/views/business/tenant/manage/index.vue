<script setup lang="tsx">
import { NDivider } from 'naive-ui';
import { fetchBatchDeleteTenant, fetchGetTenantList } from '@/service/api/business/tenant';
import { useAppStore } from '@/store/modules/app';
import { useAuth } from '@/hooks/business/auth';
import { useDownload } from '@/hooks/business/download';
import { useTable, useTableOperate } from '@/hooks/common/table';
import { $t } from '@/locales';
import ButtonIcon from '@/components/custom/button-icon.vue';
import TenantOperateDrawer from './modules/tenant-operate-drawer.vue';
import TenantSearch from './modules/tenant-search.vue';

defineOptions({
  name: 'TenantList'
});

const appStore = useAppStore();
const { download } = useDownload();
const { hasAuth } = useAuth();

const {
  columns,
  columnChecks,
  data,
  getData,
  getDataByPage,
  loading,
  mobilePagination,
  searchParams,
  resetSearchParams
} = useTable({
  apiFn: fetchGetTenantList,
  apiParams: {
    pageNum: 1,
    pageSize: 10,
    // if you want to use the searchParams in Form, you need to define the following properties, and the value is null
    // the value can not be undefined, otherwise the property in Form will not be reactive
    tenantNo: null,
    name: null,
    gender: null,
    idCardNumber: null,
    idCardFrontUrl: null,
    idCardBackUrl: null,
    dateOfBirth: null,
    nation: null,
    contactNumber: null,
    emergencyContact: null,
    emergencyContactNumber: null,
    companyInfo: null,
    householdRegister: null,
    currentAddress: null,
    checkInTime: null,
    checkOutTime: null,
    leaseTermType: null,
    expectedLeaseTerm: null,
    status: null,
    avatarUrl: null,
    params: {}
  },
  columns: () => [
    {
      type: 'selection',
      align: 'center',
      width: 48
    },
    {
      key: 'index',
      title: $t('common.index'),
      align: 'center',
      width: 64
    },
    {
      key: 'id',
      title: '主键，唯一标识每个租户',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'tenantNo',
      title: '租户编号，系统自动生成，如TEN20231015001',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'name',
      title: '租户姓名',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'gender',
      title: '性别',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'idCardNumber',
      title: '身份证号码',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'idCardFrontUrl',
      title: '身份证正面照片URL',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'idCardBackUrl',
      title: '身份证反面照片URL',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'dateOfBirth',
      title: '出生日期',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'nation',
      title: '民族',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'contactNumber',
      title: '联系方式',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'emergencyContact',
      title: '紧急联系人姓名',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'emergencyContactNumber',
      title: '紧急联系人电话',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'companyInfo',
      title: '公司信息',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'householdRegister',
      title: '户籍地址',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'currentAddress',
      title: '现住址',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'checkInTime',
      title: '入住时间',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'checkOutTime',
      title: '退租时间',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'leaseTermType',
      title: '租约类型：长期/短期',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'expectedLeaseTerm',
      title: '预计租期(月)，辅助判断租约类型',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'status',
      title: '租户状态',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'avatarUrl',
      title: '头像存储路径',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'remark',
      title: '关于租户的备注信息',
      align: 'center',
      minWidth: 120
    },
    {
      key: 'operate',
      title: $t('common.operate'),
      align: 'center',
      width: 130,
      render: row => {
        const divider = () => {
          if (!hasAuth('business:tenant:edit') || !hasAuth('business:tenant:remove')) {
            return null;
          }
          return <NDivider vertical />;
        };

        const editBtn = () => {
          if (!hasAuth('business:tenant:edit')) {
            return null;
          }
          return (
            <ButtonIcon
              text
              type="primary"
              icon="material-symbols:drive-file-rename-outline-outline"
              tooltipContent={$t('common.edit')}
              onClick={() => edit(row.id!)}
            />
          );
        };

        const deleteBtn = () => {
          if (!hasAuth('business:tenant:remove')) {
            return null;
          }
          return (
            <ButtonIcon
              text
              type="error"
              icon="material-symbols:delete-outline"
              tooltipContent={$t('common.delete')}
              popconfirmContent={$t('common.confirmDelete')}
              onPositiveClick={() => handleDelete(row.id!)}
            />
          );
        };

        return (
          <div class="flex-center gap-8px">
            {editBtn()}
            {divider()}
            {deleteBtn()}
          </div>
        );
      }
    }
  ]
});

const { drawerVisible, operateType, editingData, handleAdd, handleEdit, checkedRowKeys, onBatchDeleted, onDeleted } =
  useTableOperate(data, getData);

async function handleBatchDelete() {
  // request
  const { error } = await fetchBatchDeleteTenant(checkedRowKeys.value);
  if (error) return;
  onBatchDeleted();
}

async function handleDelete(id: CommonType.IdType) {
  // request
  const { error } = await fetchBatchDeleteTenant([id]);
  if (error) return;
  onDeleted();
}

function edit(id: CommonType.IdType) {
  handleEdit('id', id);
}

function handleExport() {
  download('/business/tenant/export', searchParams, `租户信息管理_${new Date().getTime()}.xlsx`);
}
</script>

<template>
  <div class="min-h-500px flex-col-stretch gap-16px overflow-hidden lt-sm:overflow-auto">
    <TenantSearch v-model:model="searchParams" @reset="resetSearchParams" @search="getDataByPage" />
    <NCard title="租户信息管理列表" :bordered="false" size="small" class="card-wrapper sm:flex-1-hidden">
      <template #header-extra>
        <TableHeaderOperation
          v-model:columns="columnChecks"
          :disabled-delete="checkedRowKeys.length === 0"
          :loading="loading"
          :show-add="hasAuth('business:tenant:add')"
          :show-delete="hasAuth('business:tenant:remove')"
          :show-export="hasAuth('business:tenant:export')"
          @add="handleAdd"
          @delete="handleBatchDelete"
          @export="handleExport"
          @refresh="getData"
        />
      </template>
      <NDataTable
        v-model:checked-row-keys="checkedRowKeys"
        :columns="columns"
        :data="data"
        size="small"
        :flex-height="!appStore.isMobile"
        :scroll-x="962"
        :loading="loading"
        remote
        :row-key="row => row.id"
        :pagination="mobilePagination"
        class="sm:h-full"
      />
      <TenantOperateDrawer
        v-model:visible="drawerVisible"
        :operate-type="operateType"
        :row-data="editingData"
        @submitted="getDataByPage"
      />
    </NCard>
  </div>
</template>

<style scoped></style>
